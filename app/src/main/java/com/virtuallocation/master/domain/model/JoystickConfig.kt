package com.virtuallocation.master.domain.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

/**
 * 摇杆配置数据模型
 */
@Parcelize
data class JoystickConfig(
    val size: JoystickSize = JoystickSize.MEDIUM,
    val transparency: Float = 0.7f, // 0.0 - 1.0
    val position: JoystickPosition = JoystickPosition(100, 100),
    val isPositionLocked: Boolean = false,
    val movementMode: MovementMode = MovementMode.RUNNING,
    val customSpeedKmh: Double = 12.0,
    val isEnabled: Boolean = false,
    val showSpeedIndicator: Boolean = true,
    val showTrail: Boolean = true,
    val vibrationEnabled: Boolean = true
) : Parcelable {
    
    /**
     * 获取当前速度（km/h）
     */
    fun getCurrentSpeed(): Double {
        return when (movementMode) {
            MovementMode.CUSTOM -> customSpeedKmh
            else -> movementMode.speedKmh
        }
    }
    
    /**
     * 获取摇杆半径（像素）
     */
    fun getRadius(): Int {
        return size.radius
    }
    
    /**
     * 获取透明度（0-255）
     */
    fun getAlpha(): Int {
        return (transparency * 255).toInt()
    }
}

/**
 * 摇杆大小枚举
 */
@Parcelize
enum class JoystickSize(
    val displayName: String,
    val radius: Int
) : Parcelable {
    SMALL("小", 60),
    MEDIUM("中", 80),
    LARGE("大", 100),
    EXTRA_LARGE("特大", 120)
}

/**
 * 摇杆位置
 */
@Parcelize
data class JoystickPosition(
    val x: Int,
    val y: Int
) : Parcelable

/**
 * 移动模式枚举
 */
@Parcelize
enum class MovementMode(
    val displayName: String,
    val speedKmh: Double,
    val icon: String
) : Parcelable {
    WALKING("步行", 5.0, "🚶"),
    RUNNING("跑步", 12.0, "🏃"),
    CYCLING("骑行", 20.0, "🚴"),
    DRIVING("驾车", 60.0, "🚗"),
    FLYING("飞行", 200.0, "✈️"),
    CUSTOM("自定义", 0.0, "⚙️");
    
    /**
     * 获取速度描述
     */
    fun getSpeedDescription(): String {
        return when (this) {
            CUSTOM -> "自定义速度"
            else -> "$displayName (${speedKmh.toInt()} km/h)"
        }
    }
}

/**
 * 摇杆状态
 */
@Parcelize
data class JoystickState(
    val isActive: Boolean = false,
    val deltaX: Float = 0f,
    val deltaY: Float = 0f,
    val distance: Float = 0f,
    val angle: Float = 0f,
    val currentSpeed: Double = 0.0,
    val lastUpdateTime: Long = System.currentTimeMillis()
) : Parcelable {
    
    /**
     * 是否在移动
     */
    fun isMoving(): Boolean {
        return isActive && distance > 0.1f
    }
    
    /**
     * 获取移动方向描述
     */
    fun getDirectionDescription(): String {
        if (!isMoving()) return "静止"
        
        return when ((angle + 360) % 360) {
            in 0f..22.5f, in 337.5f..360f -> "北"
            in 22.5f..67.5f -> "东北"
            in 67.5f..112.5f -> "东"
            in 112.5f..157.5f -> "东南"
            in 157.5f..202.5f -> "南"
            in 202.5f..247.5f -> "西南"
            in 247.5f..292.5f -> "西"
            in 292.5f..337.5f -> "西北"
            else -> "未知"
        }
    }
}
