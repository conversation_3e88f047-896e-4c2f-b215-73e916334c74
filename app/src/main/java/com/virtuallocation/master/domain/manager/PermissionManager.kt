package com.virtuallocation.master.domain.manager

import android.Manifest
import android.app.AppOpsManager
import android.content.Context
import android.content.pm.PackageManager
import android.location.LocationManager
import android.os.Build
import android.provider.Settings
import androidx.core.content.ContextCompat
import dagger.hilt.android.qualifiers.ApplicationContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 权限管理器
 * 负责检查和管理应用所需的各种权限
 */
@Singleton
class PermissionManager @Inject constructor(
    @ApplicationContext private val context: Context
) {
    
    /**
     * 权限状态枚举
     */
    enum class PermissionStatus {
        GRANTED,        // 已授权
        DENIED,         // 被拒绝
        NOT_REQUESTED,  // 未请求
        PERMANENTLY_DENIED // 永久拒绝
    }
    
    /**
     * 权限检查结果
     */
    data class PermissionCheckResult(
        val hasLocationPermission: Boole<PERSON>,
        val hasMockLocationPermission: <PERSON><PERSON><PERSON>,
        val hasOverlayPermission: <PERSON><PERSON>an,
        val isDeveloperOptionsEnabled: <PERSON><PERSON>an,
        val isMockLocationAppSelected: <PERSON><PERSON><PERSON>,
        val canStartMocking: Boolean
    ) {
        fun isAllPermissionsGranted(): Boolean {
            return hasLocationPermission && 
                   hasMockLocationPermission && 
                   hasOverlayPermission && 
                   isDeveloperOptionsEnabled && 
                   isMockLocationAppSelected
        }
    }
    
    /**
     * 检查所有权限状态
     */
    fun checkAllPermissions(): PermissionCheckResult {
        val hasLocationPermission = hasLocationPermission()
        val hasMockLocationPermission = hasMockLocationPermission()
        val hasOverlayPermission = hasOverlayPermission()
        val isDeveloperOptionsEnabled = isDeveloperOptionsEnabled()
        val isMockLocationAppSelected = isMockLocationAppSelected()
        
        val canStartMocking = hasLocationPermission && 
                             hasMockLocationPermission && 
                             isDeveloperOptionsEnabled && 
                             isMockLocationAppSelected
        
        return PermissionCheckResult(
            hasLocationPermission = hasLocationPermission,
            hasMockLocationPermission = hasMockLocationPermission,
            hasOverlayPermission = hasOverlayPermission,
            isDeveloperOptionsEnabled = isDeveloperOptionsEnabled,
            isMockLocationAppSelected = isMockLocationAppSelected,
            canStartMocking = canStartMocking
        )
    }
    
    /**
     * 检查位置权限
     */
    fun hasLocationPermission(): Boolean {
        val fineLocationGranted = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_FINE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        val coarseLocationGranted = ContextCompat.checkSelfPermission(
            context, Manifest.permission.ACCESS_COARSE_LOCATION
        ) == PackageManager.PERMISSION_GRANTED
        
        return fineLocationGranted || coarseLocationGranted
    }
    
    /**
     * 检查模拟位置权限
     */
    fun hasMockLocationPermission(): Boolean {
        return try {
            val appOpsManager = context.getSystemService(Context.APP_OPS_SERVICE) as AppOpsManager
            val mode = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                appOpsManager.unsafeCheckOpNoThrow(
                    AppOpsManager.OPSTR_MOCK_LOCATION,
                    android.os.Process.myUid(),
                    context.packageName
                )
            } else {
                @Suppress("DEPRECATION")
                appOpsManager.checkOpNoThrow(
                    AppOpsManager.OPSTR_MOCK_LOCATION,
                    android.os.Process.myUid(),
                    context.packageName
                )
            }
            mode == AppOpsManager.MODE_ALLOWED
        } catch (e: Exception) {
            Timber.e(e, "Error checking mock location permission")
            false
        }
    }
    
    /**
     * 检查悬浮窗权限
     */
    fun hasOverlayPermission(): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context)
        } else {
            true // Android 6.0以下默认有权限
        }
    }
    
    /**
     * 检查开发者选项是否启用
     */
    fun isDeveloperOptionsEnabled(): Boolean {
        return try {
            Settings.Global.getInt(
                context.contentResolver,
                Settings.Global.DEVELOPMENT_SETTINGS_ENABLED,
                0
            ) == 1
        } catch (e: Exception) {
            Timber.e(e, "Error checking developer options")
            false
        }
    }
    
    /**
     * 检查是否选择了模拟位置应用
     */
    fun isMockLocationAppSelected(): Boolean {
        return try {
            val mockLocationApp = Settings.Secure.getString(
                context.contentResolver,
                Settings.Secure.ALLOW_MOCK_LOCATION
            )
            
            // Android 6.0以上需要检查具体的应用
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                val selectedApp = Settings.Secure.getString(
                    context.contentResolver,
                    "mock_location_app"
                )
                selectedApp == context.packageName
            } else {
                mockLocationApp == "1"
            }
        } catch (e: Exception) {
            Timber.e(e, "Error checking mock location app selection")
            false
        }
    }
    
    /**
     * 检查GPS是否启用
     */
    fun isGpsEnabled(): Boolean {
        val locationManager = context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
        return locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)
    }
    
    /**
     * 获取需要请求的权限列表
     */
    fun getRequiredPermissions(): List<String> {
        val permissions = mutableListOf<String>()
        
        if (!hasLocationPermission()) {
            permissions.add(Manifest.permission.ACCESS_FINE_LOCATION)
            permissions.add(Manifest.permission.ACCESS_COARSE_LOCATION)
        }
        
        return permissions
    }
    
    /**
     * 获取权限状态描述
     */
    fun getPermissionStatusDescription(result: PermissionCheckResult): String {
        val issues = mutableListOf<String>()
        
        if (!result.hasLocationPermission) {
            issues.add("需要位置权限")
        }
        
        if (!result.isDeveloperOptionsEnabled) {
            issues.add("需要启用开发者选项")
        }
        
        if (!result.isMockLocationAppSelected) {
            issues.add("需要选择模拟位置应用")
        }
        
        if (!result.hasOverlayPermission) {
            issues.add("需要悬浮窗权限（摇杆功能）")
        }
        
        return when {
            issues.isEmpty() -> "所有权限已就绪"
            else -> "缺少权限: ${issues.joinToString(", ")}"
        }
    }
    
    /**
     * 获取权限设置指导步骤
     */
    fun getPermissionGuideSteps(): List<PermissionGuideStep> {
        val steps = mutableListOf<PermissionGuideStep>()
        val result = checkAllPermissions()
        
        if (!result.hasLocationPermission) {
            steps.add(
                PermissionGuideStep(
                    title = "授予位置权限",
                    description = "应用需要位置权限来提供模拟位置服务",
                    action = "点击授权按钮",
                    isCompleted = false
                )
            )
        }
        
        if (!result.isDeveloperOptionsEnabled) {
            steps.add(
                PermissionGuideStep(
                    title = "启用开发者选项",
                    description = "设置 → 关于手机 → 连续点击版本号7次",
                    action = "前往设置",
                    isCompleted = false
                )
            )
        }
        
        if (!result.isMockLocationAppSelected) {
            steps.add(
                PermissionGuideStep(
                    title = "选择模拟位置应用",
                    description = "设置 → 开发者选项 → 选择模拟位置信息应用",
                    action = "前往设置",
                    isCompleted = false
                )
            )
        }
        
        if (!result.hasOverlayPermission) {
            steps.add(
                PermissionGuideStep(
                    title = "授予悬浮窗权限",
                    description = "摇杆功能需要在其他应用上层显示",
                    action = "前往设置",
                    isCompleted = false
                )
            )
        }
        
        return steps
    }
    
    /**
     * 权限指导步骤
     */
    data class PermissionGuideStep(
        val title: String,
        val description: String,
        val action: String,
        val isCompleted: Boolean
    )
}
