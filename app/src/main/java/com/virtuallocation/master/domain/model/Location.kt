package com.virtuallocation.master.domain.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.util.UUID

/**
 * 位置数据模型
 */
@Parcelize
@Entity(tableName = "locations")
data class Location(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val latitude: Double,
    val longitude: Double,
    val address: String = "",
    val description: String = "",
    val tags: List<String> = emptyList(),
    val isFavorite: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable {
    
    /**
     * 计算与另一个位置的距离（米）
     */
    fun distanceTo(other: Location): Double {
        return distanceTo(other.latitude, other.longitude)
    }
    
    /**
     * 计算与指定经纬度的距离（米）
     */
    fun distanceTo(lat: Double, lng: Double): Double {
        val earthRadius = 6371000.0 // 地球半径（米）
        
        val lat1Rad = Math.toRadians(latitude)
        val lat2Rad = Math.toRadians(lat)
        val deltaLat = Math.toRadians(lat - latitude)
        val deltaLng = Math.toRadians(lng - longitude)
        
        val a = kotlin.math.sin(deltaLat / 2) * kotlin.math.sin(deltaLat / 2) +
                kotlin.math.cos(lat1Rad) * kotlin.math.cos(lat2Rad) *
                kotlin.math.sin(deltaLng / 2) * kotlin.math.sin(deltaLng / 2)
        val c = 2 * kotlin.math.atan2(kotlin.math.sqrt(a), kotlin.math.sqrt(1 - a))
        
        return earthRadius * c
    }
    
    /**
     * 格式化经纬度显示
     */
    fun formatCoordinates(): String {
        return "%.6f, %.6f".format(latitude, longitude)
    }
    
    companion object {
        /**
         * 创建一个默认的北京天安门位置
         */
        fun createDefault(): Location {
            return Location(
                name = "天安门广场",
                latitude = 39.9042,
                longitude = 116.3974,
                address = "北京市东城区天安门广场",
                description = "中华人民共和国首都北京市的中心广场",
                tags = listOf("地标", "旅游景点", "历史建筑")
            )
        }
        
        /**
         * 从经纬度创建位置
         */
        fun fromCoordinates(
            latitude: Double,
            longitude: Double,
            name: String = "自定义位置"
        ): Location {
            return Location(
                name = name,
                latitude = latitude,
                longitude = longitude
            )
        }
    }
}
