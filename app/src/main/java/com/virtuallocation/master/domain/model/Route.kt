package com.virtuallocation.master.domain.model

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import kotlinx.parcelize.Parcelize
import java.util.UUID

/**
 * 路线数据模型
 */
@Parcelize
@Entity(tableName = "routes")
@TypeConverters(Route.Converters::class)
data class Route(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),
    val name: String,
    val waypoints: List<Location>,
    val totalDistance: Double = 0.0,
    val estimatedDuration: Long = 0L, // 毫秒
    val simulationMode: SimulationMode = SimulationMode.ONCE,
    val speedKmh: Double = 12.0, // 默认跑步速度
    val description: String = "",
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
) : Parcelable {
    
    /**
     * 计算路线总距离
     */
    fun calculateTotalDistance(): Double {
        if (waypoints.size < 2) return 0.0
        
        var totalDistance = 0.0
        for (i in 0 until waypoints.size - 1) {
            totalDistance += waypoints[i].distanceTo(waypoints[i + 1])
        }
        return totalDistance
    }
    
    /**
     * 计算预计持续时间（毫秒）
     */
    fun calculateEstimatedDuration(): Long {
        val distanceKm = calculateTotalDistance() / 1000.0
        val durationHours = distanceKm / speedKmh
        return (durationHours * 3600 * 1000).toLong()
    }
    
    /**
     * 获取起点
     */
    fun getStartPoint(): Location? = waypoints.firstOrNull()
    
    /**
     * 获取终点
     */
    fun getEndPoint(): Location? = waypoints.lastOrNull()
    
    /**
     * 获取途经点数量
     */
    fun getWaypointCount(): Int = waypoints.size
    
    /**
     * 格式化距离显示
     */
    fun formatDistance(): String {
        val distance = calculateTotalDistance()
        return when {
            distance < 1000 -> "%.0f m".format(distance)
            else -> "%.1f km".format(distance / 1000)
        }
    }
    
    /**
     * 格式化持续时间显示
     */
    fun formatDuration(): String {
        val durationMs = calculateEstimatedDuration()
        val minutes = (durationMs / 1000 / 60).toInt()
        val hours = minutes / 60
        val remainingMinutes = minutes % 60
        
        return when {
            hours > 0 -> "${hours}小时${remainingMinutes}分钟"
            else -> "${minutes}分钟"
        }
    }
    
    /**
     * 类型转换器
     */
    class Converters {
        private val gson = Gson()
        
        @TypeConverter
        fun fromLocationList(locations: List<Location>): String {
            return gson.toJson(locations)
        }
        
        @TypeConverter
        fun toLocationList(locationsString: String): List<Location> {
            val listType = object : TypeToken<List<Location>>() {}.type
            return gson.fromJson(locationsString, listType)
        }
        
        @TypeConverter
        fun fromSimulationMode(mode: SimulationMode): String {
            return mode.name
        }
        
        @TypeConverter
        fun toSimulationMode(modeName: String): SimulationMode {
            return SimulationMode.valueOf(modeName)
        }
    }
}

/**
 * 路线模拟模式
 */
@Parcelize
enum class SimulationMode(
    val displayName: String,
    val description: String
) : Parcelable {
    ONCE("单次执行", "沿路线执行一次后停止"),
    LOOP("循环执行", "到达终点后回到起点继续执行"),
    PING_PONG("往返执行", "到达终点后原路返回起点")
}
