package com.virtuallocation.master.di

import android.content.Context
import androidx.room.Room
import com.virtuallocation.master.data.database.VirtualLocationDatabase
import com.virtuallocation.master.data.database.dao.LocationDao
import com.virtuallocation.master.data.database.dao.RouteDao
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 数据库依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object DatabaseModule {
    
    /**
     * 提供数据库实例
     */
    @Provides
    @Singleton
    fun provideVirtualLocationDatabase(
        @ApplicationContext context: Context
    ): VirtualLocationDatabase {
        return Room.databaseBuilder(
            context.applicationContext,
            VirtualLocationDatabase::class.java,
            VirtualLocationDatabase.DATABASE_NAME
        )
            .fallbackToDestructiveMigration() // 开发阶段使用，生产环境需要提供迁移策略
            .build()
    }
    
    /**
     * 提供位置DAO
     */
    @Provides
    fun provideLocationDao(database: VirtualLocationDatabase): LocationDao {
        return database.locationDao()
    }
    
    /**
     * 提供路线DAO
     */
    @Provides
    fun provideRouteDao(database: VirtualLocationDatabase): RouteDao {
        return database.routeDao()
    }
}
