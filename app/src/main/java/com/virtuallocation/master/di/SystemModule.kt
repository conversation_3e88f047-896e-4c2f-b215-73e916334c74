package com.virtuallocation.master.di

import android.content.Context
import android.location.LocationManager
import android.view.WindowManager
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

/**
 * 系统服务依赖注入模块
 */
@Module
@InstallIn(SingletonComponent::class)
object SystemModule {
    
    /**
     * 提供位置管理器
     */
    @Provides
    @Singleton
    fun provideLocationManager(
        @ApplicationContext context: Context
    ): LocationManager {
        return context.getSystemService(Context.LOCATION_SERVICE) as LocationManager
    }
    
    /**
     * 提供窗口管理器
     */
    @Provides
    @Singleton
    fun provideWindowManager(
        @ApplicationContext context: Context
    ): WindowManager {
        return context.getSystemService(Context.WINDOW_SERVICE) as WindowManager
    }
}
