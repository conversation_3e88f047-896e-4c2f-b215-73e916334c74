package com.virtuallocation.master.presentation.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.virtuallocation.master.domain.manager.PermissionManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * 主界面ViewModel
 * 负责管理主界面的状态和业务逻辑
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val permissionManager: PermissionManager
) : ViewModel() {
    
    /**
     * UI状态数据类
     */
    data class UiState(
        val isLoading: Boolean = true,
        val permissionResult: PermissionManager.PermissionCheckResult = PermissionManager.PermissionCheckResult(
            hasLocationPermission = false,
            hasMockLocationPermission = false,
            hasOverlayPermission = false,
            isDeveloperOptionsEnabled = false,
            isMockLocationAppSelected = false,
            canStartMocking = false
        ),
        val errorMessage: String? = null,
        val isPermissionDialogShown: Boolean = false
    )
    
    private val _uiState = MutableStateFlow(UiState())
    val uiState: StateFlow<UiState> = _uiState.asStateFlow()
    
    init {
        Timber.d("MainViewModel initialized")
    }
    
    /**
     * 检查所有权限
     */
    fun checkPermissions() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value.copy(isLoading = true)
                
                val permissionResult = permissionManager.checkAllPermissions()
                
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    permissionResult = permissionResult,
                    errorMessage = null
                )
                
                Timber.d("Permission check result: $permissionResult")
                
            } catch (e: Exception) {
                Timber.e(e, "Error checking permissions")
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    errorMessage = "权限检查失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 权限授权成功回调
     */
    fun onPermissionsGranted() {
        Timber.d("Permissions granted")
        checkPermissions() // 重新检查权限状态
    }
    
    /**
     * 权限被拒绝回调
     */
    fun onPermissionsDenied() {
        Timber.d("Permissions denied")
        _uiState.value = _uiState.value.copy(
            errorMessage = "需要授权位置权限才能使用虚拟定位功能"
        )
        checkPermissions() // 重新检查权限状态
    }
    
    /**
     * 清除错误消息
     */
    fun clearErrorMessage() {
        _uiState.value = _uiState.value.copy(errorMessage = null)
    }
    
    /**
     * 显示权限对话框
     */
    fun showPermissionDialog() {
        _uiState.value = _uiState.value.copy(isPermissionDialogShown = true)
    }
    
    /**
     * 隐藏权限对话框
     */
    fun hidePermissionDialog() {
        _uiState.value = _uiState.value.copy(isPermissionDialogShown = false)
    }
    
    /**
     * 获取权限状态描述
     */
    fun getPermissionStatusDescription(): String {
        return permissionManager.getPermissionStatusDescription(_uiState.value.permissionResult)
    }
    
    /**
     * 获取权限指导步骤
     */
    fun getPermissionGuideSteps(): List<PermissionManager.PermissionGuideStep> {
        return permissionManager.getPermissionGuideSteps()
    }
    
    /**
     * 获取需要请求的权限列表
     */
    fun getRequiredPermissions(): List<String> {
        return permissionManager.getRequiredPermissions()
    }
    
    /**
     * 检查是否可以开始模拟位置
     */
    fun canStartMocking(): Boolean {
        return _uiState.value.permissionResult.canStartMocking
    }
    
    /**
     * 检查特定权限状态
     */
    fun hasLocationPermission(): Boolean {
        return _uiState.value.permissionResult.hasLocationPermission
    }
    
    fun hasMockLocationPermission(): Boolean {
        return _uiState.value.permissionResult.hasMockLocationPermission
    }
    
    fun hasOverlayPermission(): Boolean {
        return _uiState.value.permissionResult.hasOverlayPermission
    }
    
    fun isDeveloperOptionsEnabled(): Boolean {
        return _uiState.value.permissionResult.isDeveloperOptionsEnabled
    }
    
    fun isMockLocationAppSelected(): Boolean {
        return _uiState.value.permissionResult.isMockLocationAppSelected
    }
}
