package com.virtuallocation.master.presentation.ui.theme

import androidx.compose.ui.graphics.Color

val Purple80 = Color(0xFFD0BCFF)
val PurpleGrey80 = Color(0xFFCCC2DC)
val Pink80 = Color(0xFFEFB8C8)

val Purple40 = Color(0xFF6650a4)
val PurpleGrey40 = Color(0xFF625b71)
val Pink40 = Color(0xFF7D5260)

// 虚拟定位大师专用颜色
val LocationPrimary = Color(0xFF2196F3) // 蓝色主色调
val LocationSecondary = Color(0xFF4CAF50) // 绿色辅助色
val LocationAccent = Color(0xFFFF9800) // 橙色强调色

val LocationSuccess = Color(0xFF4CAF50) // 成功状态
val LocationWarning = Color(0xFFFF9800) // 警告状态
val LocationError = Color(0xFFF44336) // 错误状态
val LocationInfo = Color(0xFF2196F3) // 信息状态

// 地图相关颜色
val MapMarker = Color(0xFFE91E63) // 地图标记颜色
val MapRoute = Color(0xFF3F51B5) // 路线颜色
val MapTrail = Color(0xFF9C27B0) // 轨迹颜色

// 摇杆相关颜色
val JoystickBase = Color(0x80000000) // 摇杆底盘颜色（半透明黑色）
val JoystickKnob = Color(0xFF2196F3) // 摇杆控制点颜色
val JoystickActive = Color(0xFF4CAF50) // 摇杆激活状态颜色
