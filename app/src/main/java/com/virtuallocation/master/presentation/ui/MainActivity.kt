package com.virtuallocation.master.presentation.ui

import android.Manifest
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.Settings
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.virtuallocation.master.R
import com.virtuallocation.master.presentation.ui.theme.VirtualLocationMasterTheme
import com.virtuallocation.master.presentation.viewmodel.MainViewModel
import dagger.hilt.android.AndroidEntryPoint
import timber.log.Timber

/**
 * 主Activity
 * 应用的入口点，负责权限检查和主界面显示
 */
@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    
    private val viewModel: MainViewModel by viewModels()
    
    // 权限请求启动器
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            viewModel.onPermissionsGranted()
        } else {
            viewModel.onPermissionsDenied()
        }
    }
    
    // 悬浮窗权限请求启动器
    private val overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) {
        viewModel.checkPermissions()
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // 安装启动屏幕
        installSplashScreen()
        
        super.onCreate(savedInstanceState)
        
        // 初始化权限检查
        viewModel.checkPermissions()
        
        setContent {
            VirtualLocationMasterTheme {
                MainScreen(
                    viewModel = viewModel,
                    onRequestPermissions = ::requestPermissions,
                    onRequestOverlayPermission = ::requestOverlayPermission,
                    onOpenDeveloperSettings = ::openDeveloperSettings
                )
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        // 每次回到前台都检查权限状态
        viewModel.checkPermissions()
    }
    
    /**
     * 请求基础权限
     */
    private fun requestPermissions(permissions: List<String>) {
        permissionLauncher.launch(permissions.toTypedArray())
    }
    
    /**
     * 请求悬浮窗权限
     */
    private fun requestOverlayPermission() {
        val intent = Intent(
            Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
            Uri.parse("package:$packageName")
        )
        overlayPermissionLauncher.launch(intent)
    }
    
    /**
     * 打开开发者设置
     */
    private fun openDeveloperSettings() {
        try {
            val intent = Intent(Settings.ACTION_APPLICATION_DEVELOPMENT_SETTINGS)
            startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e, "Failed to open developer settings")
            // 如果无法打开开发者设置，尝试打开应用设置
            val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
                data = Uri.parse("package:$packageName")
            }
            startActivity(intent)
        }
    }
}

/**
 * 主界面Compose组件
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    onRequestPermissions: (List<String>) -> Unit,
    onRequestOverlayPermission: () -> Unit,
    onOpenDeveloperSettings: () -> Unit
) {
    val uiState by viewModel.uiState.collectAsStateWithLifecycle()
    val context = LocalContext.current
    
    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.app_name)) },
                actions = {
                    IconButton(onClick = { /* TODO: 打开设置 */ }) {
                        Icon(
                            imageVector = androidx.compose.material.icons.Icons.Default.Settings,
                            contentDescription = "设置"
                        )
                    }
                }
            )
        }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            when {
                uiState.isLoading -> {
                    LoadingScreen()
                }
                
                !uiState.permissionResult.isAllPermissionsGranted() -> {
                    PermissionScreen(
                        permissionResult = uiState.permissionResult,
                        onRequestPermissions = onRequestPermissions,
                        onRequestOverlayPermission = onRequestOverlayPermission,
                        onOpenDeveloperSettings = onOpenDeveloperSettings
                    )
                }
                
                else -> {
                    MainContentScreen(
                        viewModel = viewModel,
                        uiState = uiState
                    )
                }
            }
        }
    }
}

/**
 * 加载屏幕
 */
@Composable
fun LoadingScreen() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            CircularProgressIndicator()
            Text(
                text = "正在初始化...",
                style = MaterialTheme.typography.bodyLarge
            )
        }
    }
}

/**
 * 权限请求屏幕
 */
@Composable
fun PermissionScreen(
    permissionResult: com.virtuallocation.master.domain.manager.PermissionManager.PermissionCheckResult,
    onRequestPermissions: (List<String>) -> Unit,
    onRequestOverlayPermission: () -> Unit,
    onOpenDeveloperSettings: () -> Unit
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "权限设置",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Text(
            text = "为了正常使用虚拟定位功能，需要设置以下权限：",
            style = MaterialTheme.typography.bodyMedium
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 位置权限
        PermissionItem(
            title = "位置权限",
            description = "用于提供模拟位置服务",
            isGranted = permissionResult.hasLocationPermission,
            onAction = {
                onRequestPermissions(listOf(
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION
                ))
            }
        )
        
        // 开发者选项
        PermissionItem(
            title = "开发者选项",
            description = "设置 → 关于手机 → 连续点击版本号7次",
            isGranted = permissionResult.isDeveloperOptionsEnabled,
            onAction = onOpenDeveloperSettings
        )
        
        // 模拟位置应用
        PermissionItem(
            title = "模拟位置应用",
            description = "开发者选项 → 选择模拟位置信息应用",
            isGranted = permissionResult.isMockLocationAppSelected,
            onAction = onOpenDeveloperSettings
        )
        
        // 悬浮窗权限
        PermissionItem(
            title = "悬浮窗权限",
            description = "摇杆功能需要在其他应用上层显示",
            isGranted = permissionResult.hasOverlayPermission,
            onAction = onRequestOverlayPermission
        )
    }
}

/**
 * 权限项组件
 */
@Composable
fun PermissionItem(
    title: String,
    description: String,
    isGranted: Boolean,
    onAction: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = if (isGranted) 
                MaterialTheme.colorScheme.primaryContainer 
            else 
                MaterialTheme.colorScheme.errorContainer
        )
    ) {
        Column(
            modifier = Modifier.padding(16.dp)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = title,
                        style = MaterialTheme.typography.titleMedium
                    )
                    Text(
                        text = description,
                        style = MaterialTheme.typography.bodySmall
                    )
                }
                
                if (isGranted) {
                    Icon(
                        imageVector = androidx.compose.material.icons.Icons.Default.Check,
                        contentDescription = "已授权",
                        tint = MaterialTheme.colorScheme.primary
                    )
                } else {
                    Button(onClick = onAction) {
                        Text("设置")
                    }
                }
            }
        }
    }
}

/**
 * 主要内容屏幕
 */
@Composable
fun MainContentScreen(
    viewModel: MainViewModel,
    uiState: com.virtuallocation.master.presentation.viewmodel.MainViewModel.UiState
) {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        Text(
            text = "虚拟定位大师",
            style = MaterialTheme.typography.headlineMedium
        )
        
        Text(
            text = "所有权限已就绪，可以开始使用！",
            style = MaterialTheme.typography.bodyMedium
        )
        
        // TODO: 添加地图和控制界面
        Button(
            onClick = { /* TODO: 实现功能 */ },
            modifier = Modifier.fillMaxWidth()
        ) {
            Text("开始模拟位置")
        }
    }
}
