package com.virtuallocation.master

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.os.Build
import androidx.core.content.ContextCompat
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber

/**
 * 虚拟定位大师应用程序类
 * 负责应用程序的全局初始化工作
 */
@HiltAndroidApp
class VirtualLocationApplication : Application() {

    companion object {
        const val MOCK_LOCATION_CHANNEL_ID = "mock_location_channel"
        const val JOYSTICK_CHANNEL_ID = "joystick_channel"
        const val ROUTE_SIMULATION_CHANNEL_ID = "route_simulation_channel"
    }

    override fun onCreate() {
        super.onCreate()
        
        // 初始化日志系统
        initializeLogging()
        
        // 创建通知渠道
        createNotificationChannels()
        
        Timber.d("VirtualLocationApplication initialized")
    }

    /**
     * 初始化日志系统
     */
    private fun initializeLogging() {
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        } else {
            // 生产环境可以植入崩溃报告树
            // Timber.plant(CrashReportingTree())
        }
    }

    /**
     * 创建通知渠道
     */
    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = ContextCompat.getSystemService(
                this,
                NotificationManager::class.java
            ) as NotificationManager

            // 模拟位置服务通知渠道
            val mockLocationChannel = NotificationChannel(
                MOCK_LOCATION_CHANNEL_ID,
                getString(R.string.mock_location_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.mock_location_channel_description)
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            // 摇杆控制通知渠道
            val joystickChannel = NotificationChannel(
                JOYSTICK_CHANNEL_ID,
                getString(R.string.joystick_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.joystick_channel_description)
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            // 路线模拟通知渠道
            val routeChannel = NotificationChannel(
                ROUTE_SIMULATION_CHANNEL_ID,
                getString(R.string.route_simulation_channel_name),
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = getString(R.string.route_simulation_channel_description)
                setShowBadge(false)
                enableLights(false)
                enableVibration(false)
            }

            notificationManager.createNotificationChannels(
                listOf(mockLocationChannel, joystickChannel, routeChannel)
            )
        }
    }
}
