package com.virtuallocation.master.data.database.dao

import androidx.room.*
import com.virtuallocation.master.domain.model.Location
import kotlinx.coroutines.flow.Flow

/**
 * 位置数据访问对象
 */
@Dao
interface LocationDao {
    
    /**
     * 获取所有位置
     */
    @Query("SELECT * FROM locations ORDER BY updatedAt DESC")
    fun getAllLocations(): Flow<List<Location>>
    
    /**
     * 获取收藏的位置
     */
    @Query("SELECT * FROM locations WHERE isFavorite = 1 ORDER BY updatedAt DESC")
    fun getFavoriteLocations(): Flow<List<Location>>
    
    /**
     * 根据ID获取位置
     */
    @Query("SELECT * FROM locations WHERE id = :id")
    suspend fun getLocationById(id: String): Location?
    
    /**
     * 搜索位置
     */
    @Query("""
        SELECT * FROM locations 
        WHERE name LIKE '%' || :query || '%' 
        OR address LIKE '%' || :query || '%' 
        OR description LIKE '%' || :query || '%'
        ORDER BY updatedAt DESC
    """)
    fun searchLocations(query: String): Flow<List<Location>>
    
    /**
     * 根据标签搜索位置
     */
    @Query("SELECT * FROM locations WHERE tags LIKE '%' || :tag || '%' ORDER BY updatedAt DESC")
    fun getLocationsByTag(tag: String): Flow<List<Location>>
    
    /**
     * 获取最近使用的位置
     */
    @Query("SELECT * FROM locations ORDER BY updatedAt DESC LIMIT :limit")
    fun getRecentLocations(limit: Int = 10): Flow<List<Location>>
    
    /**
     * 插入位置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLocation(location: Location)
    
    /**
     * 插入多个位置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertLocations(locations: List<Location>)
    
    /**
     * 更新位置
     */
    @Update
    suspend fun updateLocation(location: Location)
    
    /**
     * 删除位置
     */
    @Delete
    suspend fun deleteLocation(location: Location)
    
    /**
     * 根据ID删除位置
     */
    @Query("DELETE FROM locations WHERE id = :id")
    suspend fun deleteLocationById(id: String)
    
    /**
     * 删除所有位置
     */
    @Query("DELETE FROM locations")
    suspend fun deleteAllLocations()
    
    /**
     * 切换收藏状态
     */
    @Query("UPDATE locations SET isFavorite = :isFavorite, updatedAt = :updatedAt WHERE id = :id")
    suspend fun toggleFavorite(id: String, isFavorite: Boolean, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * 更新位置的最后使用时间
     */
    @Query("UPDATE locations SET updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateLastUsedTime(id: String, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * 获取位置总数
     */
    @Query("SELECT COUNT(*) FROM locations")
    suspend fun getLocationCount(): Int
    
    /**
     * 获取收藏位置总数
     */
    @Query("SELECT COUNT(*) FROM locations WHERE isFavorite = 1")
    suspend fun getFavoriteLocationCount(): Int
    
    /**
     * 检查位置是否存在
     */
    @Query("SELECT EXISTS(SELECT 1 FROM locations WHERE id = :id)")
    suspend fun locationExists(id: String): Boolean
    
    /**
     * 根据坐标查找相似位置（误差范围内）
     */
    @Query("""
        SELECT * FROM locations 
        WHERE ABS(latitude - :latitude) < :tolerance 
        AND ABS(longitude - :longitude) < :tolerance
        ORDER BY updatedAt DESC
    """)
    suspend fun findSimilarLocations(
        latitude: Double, 
        longitude: Double, 
        tolerance: Double = 0.001
    ): List<Location>
}
