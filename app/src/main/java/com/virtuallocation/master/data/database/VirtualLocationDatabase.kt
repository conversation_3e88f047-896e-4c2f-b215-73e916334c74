package com.virtuallocation.master.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context
import com.virtuallocation.master.data.database.dao.LocationDao
import com.virtuallocation.master.data.database.dao.RouteDao
import com.virtuallocation.master.domain.model.Location
import com.virtuallocation.master.domain.model.Route

/**
 * 虚拟定位应用数据库
 */
@Database(
    entities = [Location::class, Route::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(DatabaseConverters::class)
abstract class VirtualLocationDatabase : RoomDatabase() {
    
    abstract fun locationDao(): LocationDao
    abstract fun routeDao(): RouteDao
    
    companion object {
        const val DATABASE_NAME = "virtual_location_database"
        
        @Volatile
        private var INSTANCE: VirtualLocationDatabase? = null
        
        fun getDatabase(context: Context): VirtualLocationDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    VirtualLocationDatabase::class.java,
                    DATABASE_NAME
                )
                    .addCallback(DatabaseCallback())
                    .build()
                INSTANCE = instance
                instance
            }
        }
        
        /**
         * 数据库回调，用于初始化数据
         */
        private class DatabaseCallback : RoomDatabase.Callback() {
            override fun onCreate(db: SupportSQLiteDatabase) {
                super.onCreate(db)
                // 可以在这里插入初始数据
            }
        }
        
        /**
         * 数据库迁移（如果需要）
         */
        val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // 迁移逻辑
            }
        }
    }
}
