package com.virtuallocation.master.data.service

import android.app.*
import android.content.Context
import android.content.Intent
import android.location.Criteria
import android.location.Location
import android.location.LocationManager
import android.os.Binder
import android.os.Build
import android.os.IBinder
import android.os.SystemClock
import androidx.core.app.NotificationCompat
import com.virtuallocation.master.R
import com.virtuallocation.master.VirtualLocationApplication
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.*
import timber.log.Timber
import javax.inject.Inject

/**
 * 模拟位置服务
 * 负责持续提供虚拟位置信息
 */
@AndroidEntryPoint
class MockLocationService : Service() {
    
    companion object {
        const val ACTION_START_MOCK = "ACTION_START_MOCK"
        const val ACTION_STOP_MOCK = "ACTION_STOP_MOCK"
        const val ACTION_UPDATE_LOCATION = "ACTION_UPDATE_LOCATION"
        const val ACTION_JOYSTICK_MOVE = "ACTION_JOYSTICK_MOVE"
        
        const val EXTRA_LATITUDE = "EXTRA_LATITUDE"
        const val EXTRA_LONGITUDE = "EXTRA_LONGITUDE"
        const val EXTRA_DELTA_X = "EXTRA_DELTA_X"
        const val EXTRA_DELTA_Y = "EXTRA_DELTA_Y"
        const val EXTRA_SPEED_KMH = "EXTRA_SPEED_KMH"
        
        const val NOTIFICATION_ID = 1001
        
        private const val UPDATE_INTERVAL_MS = 1000L // 1秒更新一次
    }
    
    @Inject
    lateinit var locationManager: LocationManager
    
    private val binder = MockLocationBinder()
    private val serviceScope = CoroutineScope(Dispatchers.IO + SupervisorJob())
    
    private var isMocking = false
    private var currentLatitude = 0.0
    private var currentLongitude = 0.0
    private var lastUpdateTime = 0L
    
    // 摇杆控制相关
    private var joystickDeltaX = 0f
    private var joystickDeltaY = 0f
    private var currentSpeedKmh = 12.0 // 默认跑步速度
    
    private var updateJob: Job? = null
    
    inner class MockLocationBinder : Binder() {
        fun getService(): MockLocationService = this@MockLocationService
    }
    
    override fun onBind(intent: Intent?): IBinder = binder
    
    override fun onCreate() {
        super.onCreate()
        Timber.d("MockLocationService created")
        setupLocationProvider()
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_MOCK -> {
                val latitude = intent.getDoubleExtra(EXTRA_LATITUDE, 0.0)
                val longitude = intent.getDoubleExtra(EXTRA_LONGITUDE, 0.0)
                startMockLocation(latitude, longitude)
            }
            ACTION_STOP_MOCK -> {
                stopMockLocation()
            }
            ACTION_UPDATE_LOCATION -> {
                val latitude = intent.getDoubleExtra(EXTRA_LATITUDE, 0.0)
                val longitude = intent.getDoubleExtra(EXTRA_LONGITUDE, 0.0)
                updateLocation(latitude, longitude)
            }
            ACTION_JOYSTICK_MOVE -> {
                val deltaX = intent.getFloatExtra(EXTRA_DELTA_X, 0f)
                val deltaY = intent.getFloatExtra(EXTRA_DELTA_Y, 0f)
                val speedKmh = intent.getDoubleExtra(EXTRA_SPEED_KMH, currentSpeedKmh)
                handleJoystickMove(deltaX, deltaY, speedKmh)
            }
        }
        
        return START_STICKY // 服务被杀死后自动重启
    }
    
    override fun onDestroy() {
        super.onDestroy()
        stopMockLocation()
        serviceScope.cancel()
        Timber.d("MockLocationService destroyed")
    }
    
    /**
     * 设置位置提供者
     */
    private fun setupLocationProvider() {
        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                locationManager.addTestProvider(
                    LocationManager.GPS_PROVIDER,
                    false, false, false, false,
                    true, true, true,
                    Criteria.POWER_LOW, Criteria.ACCURACY_FINE
                )
                locationManager.setTestProviderEnabled(LocationManager.GPS_PROVIDER, true)
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to setup location provider")
        }
    }
    
    /**
     * 开始模拟位置
     */
    fun startMockLocation(latitude: Double, longitude: Double) {
        if (isMocking) {
            updateLocation(latitude, longitude)
            return
        }
        
        currentLatitude = latitude
        currentLongitude = longitude
        isMocking = true
        lastUpdateTime = System.currentTimeMillis()
        
        startForeground(NOTIFICATION_ID, createNotification())
        startLocationUpdates()
        
        Timber.d("Started mock location: $latitude, $longitude")
    }
    
    /**
     * 停止模拟位置
     */
    fun stopMockLocation() {
        if (!isMocking) return
        
        isMocking = false
        joystickDeltaX = 0f
        joystickDeltaY = 0f
        
        updateJob?.cancel()
        
        try {
            locationManager.removeTestProvider(LocationManager.GPS_PROVIDER)
        } catch (e: Exception) {
            Timber.e(e, "Failed to remove test provider")
        }
        
        stopForeground(true)
        stopSelf()
        
        Timber.d("Stopped mock location")
    }
    
    /**
     * 更新位置
     */
    fun updateLocation(latitude: Double, longitude: Double) {
        if (!isMocking) return
        
        currentLatitude = latitude
        currentLongitude = longitude
        setMockLocation(latitude, longitude)
    }
    
    /**
     * 处理摇杆移动
     */
    private fun handleJoystickMove(deltaX: Float, deltaY: Float, speedKmh: Double) {
        joystickDeltaX = deltaX
        joystickDeltaY = deltaY
        currentSpeedKmh = speedKmh
    }
    
    /**
     * 开始位置更新
     */
    private fun startLocationUpdates() {
        updateJob = serviceScope.launch {
            while (isMocking) {
                try {
                    // 处理摇杆移动
                    if (joystickDeltaX != 0f || joystickDeltaY != 0f) {
                        val currentTime = System.currentTimeMillis()
                        val deltaTime = currentTime - lastUpdateTime
                        
                        val newLocation = calculateNewLocation(
                            currentLatitude, currentLongitude,
                            joystickDeltaX, joystickDeltaY,
                            currentSpeedKmh, deltaTime
                        )
                        
                        currentLatitude = newLocation.first
                        currentLongitude = newLocation.second
                        lastUpdateTime = currentTime
                    }
                    
                    // 设置模拟位置
                    setMockLocation(currentLatitude, currentLongitude)
                    
                    // 更新通知
                    updateNotification()
                    
                } catch (e: Exception) {
                    Timber.e(e, "Error updating location")
                }
                
                delay(UPDATE_INTERVAL_MS)
            }
        }
    }
    
    /**
     * 设置模拟位置
     */
    private fun setMockLocation(latitude: Double, longitude: Double) {
        try {
            val mockLocation = Location(LocationManager.GPS_PROVIDER).apply {
                this.latitude = latitude
                this.longitude = longitude
                this.accuracy = 1.0f
                this.time = System.currentTimeMillis()
                this.elapsedRealtimeNanos = SystemClock.elapsedRealtimeNanos()
                
                // 添加一些随机抖动以提高真实性
                this.latitude += (Math.random() - 0.5) * 0.00001
                this.longitude += (Math.random() - 0.5) * 0.00001
            }
            
            locationManager.setTestProviderLocation(LocationManager.GPS_PROVIDER, mockLocation)
        } catch (e: Exception) {
            Timber.e(e, "Failed to set mock location")
        }
    }
    
    /**
     * 根据摇杆输入计算新位置
     */
    private fun calculateNewLocation(
        currentLat: Double,
        currentLng: Double,
        deltaX: Float,
        deltaY: Float,
        speedKmh: Double,
        deltaTimeMs: Long
    ): Pair<Double, Double> {
        if (deltaX == 0f && deltaY == 0f) {
            return Pair(currentLat, currentLng)
        }
        
        // 将速度转换为每毫秒的距离（米）
        val speedMeterPerMs = (speedKmh * 1000) / (60 * 60 * 1000)
        val distanceM = speedMeterPerMs * deltaTimeMs
        
        // 计算移动距离
        val moveDistance = distanceM * kotlin.math.sqrt(deltaX * deltaX + deltaY * deltaY)
        
        // 地球半径（米）
        val earthRadius = 6371000.0
        
        // 计算新的经纬度
        val deltaLat = (moveDistance * (-deltaY)) / earthRadius * (180 / Math.PI)
        val deltaLng = (moveDistance * deltaX) / 
            (earthRadius * kotlin.math.cos(currentLat * Math.PI / 180)) * (180 / Math.PI)
        
        return Pair(
            currentLat + deltaLat,
            currentLng + deltaLng
        )
    }
    
    /**
     * 创建前台服务通知
     */
    private fun createNotification(): Notification {
        val intent = packageManager.getLaunchIntentForPackage(packageName)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )
        
        return NotificationCompat.Builder(this, VirtualLocationApplication.MOCK_LOCATION_CHANNEL_ID)
            .setContentTitle(getString(R.string.mock_location_service_title))
            .setContentText(getString(R.string.mock_location_service_content))
            .setSmallIcon(R.drawable.ic_location)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
    }
    
    /**
     * 更新通知内容
     */
    private fun updateNotification() {
        val notification = NotificationCompat.Builder(this, VirtualLocationApplication.MOCK_LOCATION_CHANNEL_ID)
            .setContentTitle(getString(R.string.mock_location_service_title))
            .setContentText("位置: ${String.format("%.6f, %.6f", currentLatitude, currentLongitude)}")
            .setSmallIcon(R.drawable.ic_location)
            .setOngoing(true)
            .setAutoCancel(false)
            .build()
        
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }
    
    /**
     * 获取当前模拟状态
     */
    fun isMockingLocation(): Boolean = isMocking
    
    /**
     * 获取当前位置
     */
    fun getCurrentLocation(): Pair<Double, Double> = Pair(currentLatitude, currentLongitude)
}
