package com.virtuallocation.master.data.database.dao

import androidx.room.*
import com.virtuallocation.master.domain.model.Route
import com.virtuallocation.master.domain.model.SimulationMode
import kotlinx.coroutines.flow.Flow

/**
 * 路线数据访问对象
 */
@Dao
interface RouteDao {
    
    /**
     * 获取所有路线
     */
    @Query("SELECT * FROM routes ORDER BY updatedAt DESC")
    fun getAllRoutes(): Flow<List<Route>>
    
    /**
     * 根据ID获取路线
     */
    @Query("SELECT * FROM routes WHERE id = :id")
    suspend fun getRouteById(id: String): Route?
    
    /**
     * 搜索路线
     */
    @Query("""
        SELECT * FROM routes 
        WHERE name LIKE '%' || :query || '%' 
        OR description LIKE '%' || :query || '%'
        ORDER BY updatedAt DESC
    """)
    fun searchRoutes(query: String): Flow<List<Route>>
    
    /**
     * 根据模拟模式获取路线
     */
    @Query("SELECT * FROM routes WHERE simulationMode = :mode ORDER BY updatedAt DESC")
    fun getRoutesByMode(mode: SimulationMode): Flow<List<Route>>
    
    /**
     * 获取最近使用的路线
     */
    @Query("SELECT * FROM routes ORDER BY updatedAt DESC LIMIT :limit")
    fun getRecentRoutes(limit: Int = 10): Flow<List<Route>>
    
    /**
     * 根据距离范围获取路线
     */
    @Query("SELECT * FROM routes WHERE totalDistance BETWEEN :minDistance AND :maxDistance ORDER BY updatedAt DESC")
    fun getRoutesByDistanceRange(minDistance: Double, maxDistance: Double): Flow<List<Route>>
    
    /**
     * 根据持续时间范围获取路线
     */
    @Query("SELECT * FROM routes WHERE estimatedDuration BETWEEN :minDuration AND :maxDuration ORDER BY updatedAt DESC")
    fun getRoutesByDurationRange(minDuration: Long, maxDuration: Long): Flow<List<Route>>
    
    /**
     * 插入路线
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRoute(route: Route)
    
    /**
     * 插入多个路线
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertRoutes(routes: List<Route>)
    
    /**
     * 更新路线
     */
    @Update
    suspend fun updateRoute(route: Route)
    
    /**
     * 删除路线
     */
    @Delete
    suspend fun deleteRoute(route: Route)
    
    /**
     * 根据ID删除路线
     */
    @Query("DELETE FROM routes WHERE id = :id")
    suspend fun deleteRouteById(id: String)
    
    /**
     * 删除所有路线
     */
    @Query("DELETE FROM routes")
    suspend fun deleteAllRoutes()
    
    /**
     * 更新路线的最后使用时间
     */
    @Query("UPDATE routes SET updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateLastUsedTime(id: String, updatedAt: Long = System.currentTimeMillis())
    
    /**
     * 更新路线的模拟模式
     */
    @Query("UPDATE routes SET simulationMode = :mode, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateSimulationMode(
        id: String, 
        mode: SimulationMode, 
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * 更新路线的速度
     */
    @Query("UPDATE routes SET speedKmh = :speedKmh, updatedAt = :updatedAt WHERE id = :id")
    suspend fun updateSpeed(
        id: String, 
        speedKmh: Double, 
        updatedAt: Long = System.currentTimeMillis()
    )
    
    /**
     * 获取路线总数
     */
    @Query("SELECT COUNT(*) FROM routes")
    suspend fun getRouteCount(): Int
    
    /**
     * 检查路线是否存在
     */
    @Query("SELECT EXISTS(SELECT 1 FROM routes WHERE id = :id)")
    suspend fun routeExists(id: String): Boolean
    
    /**
     * 获取路线统计信息
     */
    @Query("""
        SELECT 
            COUNT(*) as totalRoutes,
            AVG(totalDistance) as avgDistance,
            AVG(estimatedDuration) as avgDuration,
            MAX(totalDistance) as maxDistance,
            MIN(totalDistance) as minDistance
        FROM routes
    """)
    suspend fun getRouteStatistics(): RouteStatistics
}

/**
 * 路线统计信息数据类
 */
data class RouteStatistics(
    val totalRoutes: Int,
    val avgDistance: Double,
    val avgDuration: Long,
    val maxDistance: Double,
    val minDistance: Double
)
