package com.virtuallocation.master.data.database

import androidx.room.TypeConverter
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.virtuallocation.master.domain.model.Location
import com.virtuallocation.master.domain.model.SimulationMode

/**
 * Room数据库类型转换器
 */
class DatabaseConverters {
    
    private val gson = Gson()
    
    /**
     * 字符串列表转换
     */
    @TypeConverter
    fun fromStringList(value: List<String>): String {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toStringList(value: String): List<String> {
        val listType = object : TypeToken<List<String>>() {}.type
        return gson.fromJson(value, listType) ?: emptyList()
    }
    
    /**
     * 位置列表转换
     */
    @TypeConverter
    fun fromLocationList(value: List<Location>): String {
        return gson.toJson(value)
    }
    
    @TypeConverter
    fun toLocationList(value: String): List<Location> {
        val listType = object : TypeToken<List<Location>>() {}.type
        return gson.fromJson(value, listType) ?: emptyList()
    }
    
    /**
     * 模拟模式转换
     */
    @TypeConverter
    fun fromSimulationMode(value: SimulationMode): String {
        return value.name
    }
    
    @TypeConverter
    fun toSimulationMode(value: String): SimulationMode {
        return try {
            SimulationMode.valueOf(value)
        } catch (e: IllegalArgumentException) {
            SimulationMode.ONCE
        }
    }
}
