<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 位置权限 -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_MOCK_LOCATION" />
    
    <!-- 网络权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    
    <!-- 系统级浮动窗口权限 -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
    
    <!-- 前台服务权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
    
    <!-- 存储权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    
    <!-- 唤醒锁权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    
    <!-- 开机自启权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />

    <application
        android:name=".VirtualLocationApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.VirtualLocationMaster"
        android:requestLegacyExternalStorage="true"
        tools:targetApi="31">

        <!-- 高德地图API Key -->
        <meta-data
            android:name="com.amap.api.v2.apikey"
            android:value="YOUR_AMAP_API_KEY" />

        <!-- 主Activity -->
        <activity
            android:name=".presentation.ui.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.VirtualLocationMaster"
            android:screenOrientation="portrait"
            android:launchMode="singleTop">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 设置Activity -->
        <activity
            android:name=".presentation.ui.SettingsActivity"
            android:exported="false"
            android:parentActivityName=".presentation.ui.MainActivity"
            android:theme="@style/Theme.VirtualLocationMaster" />

        <!-- 权限引导Activity -->
        <activity
            android:name=".presentation.ui.PermissionGuideActivity"
            android:exported="false"
            android:theme="@style/Theme.VirtualLocationMaster.NoActionBar" />

        <!-- 模拟位置服务 -->
        <service
            android:name=".data.service.MockLocationService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="location" />

        <!-- 摇杆浮动服务 -->
        <service
            android:name=".data.service.JoystickOverlayService"
            android:enabled="true"
            android:exported="false" />

        <!-- 开机自启广播接收器 -->
        <receiver
            android:name=".data.receiver.BootReceiver"
            android:enabled="true"
            android:exported="true">
            <intent-filter android:priority="1000">
                <action android:name="android.intent.action.BOOT_COMPLETED" />
                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </receiver>

        <!-- 文件提供者 -->
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths" />
        </provider>

    </application>

</manifest>
