# 核心技术实现路径

## 1. 摇杆模式技术实现

### 1.1 系统级浮动摇杆实现

#### 核心技术方案
使用 `WindowManager` + `TYPE_APPLICATION_OVERLAY` 创建系统级浮动窗口：

```kotlin
class JoystickOverlayService : Service() {
    private lateinit var windowManager: WindowManager
    private lateinit var joystickView: JoystickView
    
    override fun onCreate() {
        super.onCreate()
        windowManager = getSystemService(WINDOW_SERVICE) as WindowManager
        createJoystickOverlay()
    }
    
    private fun createJoystickOverlay() {
        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.WRAP_CONTENT,
            WindowManager.LayoutParams.WRAP_CONTENT,
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
            } else {
                WindowManager.LayoutParams.TYPE_PHONE
            },
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        )
        
        params.gravity = Gravity.TOP or Gravity.START
        params.x = 100
        params.y = 100
        
        joystickView = JoystickView(this)
        windowManager.addView(joystickView, params)
    }
}
```

#### 自定义摇杆View实现
```kotlin
class JoystickView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    
    private var centerX = 0f
    private var centerY = 0f
    private var baseRadius = 100f
    private var knobRadius = 30f
    private var knobX = 0f
    private var knobY = 0f
    
    private val basePaint = Paint().apply {
        color = Color.GRAY
        alpha = 180
        isAntiAlias = true
    }
    
    private val knobPaint = Paint().apply {
        color = Color.BLUE
        alpha = 200
        isAntiAlias = true
    }
    
    override fun onTouchEvent(event: MotionEvent): Boolean {
        when (event.action) {
            MotionEvent.ACTION_DOWN, MotionEvent.ACTION_MOVE -> {
                val dx = event.x - centerX
                val dy = event.y - centerY
                val distance = sqrt(dx * dx + dy * dy)
                
                if (distance <= baseRadius) {
                    knobX = event.x
                    knobY = event.y
                } else {
                    val ratio = baseRadius / distance
                    knobX = centerX + dx * ratio
                    knobY = centerY + dy * ratio
                }
                
                // 计算移动向量并通知位置更新
                val moveX = (knobX - centerX) / baseRadius
                val moveY = (knobY - centerY) / baseRadius
                onJoystickMove(moveX, moveY)
                
                invalidate()
                return true
            }
            MotionEvent.ACTION_UP -> {
                knobX = centerX
                knobY = centerY
                onJoystickMove(0f, 0f)
                invalidate()
                return true
            }
        }
        return super.onTouchEvent(event)
    }
    
    private fun onJoystickMove(deltaX: Float, deltaY: Float) {
        // 发送移动事件到LocationService
        val intent = Intent(context, MockLocationService::class.java).apply {
            action = "JOYSTICK_MOVE"
            putExtra("deltaX", deltaX)
            putExtra("deltaY", deltaY)
        }
        context.startService(intent)
    }
}
```

### 1.2 移动计算与速度控制

#### 移动速度配置
```kotlin
enum class MovementMode(val speedKmh: Double, val displayName: String) {
    WALKING(5.0, "步行"),
    RUNNING(12.0, "跑步"),
    CYCLING(20.0, "骑行"),
    DRIVING(60.0, "驾车"),
    CUSTOM(0.0, "自定义")
}

class MovementCalculator {
    fun calculateNewPosition(
        currentLat: Double,
        currentLng: Double,
        deltaX: Float,
        deltaY: Float,
        speedKmh: Double,
        deltaTimeMs: Long
    ): Pair<Double, Double> {
        
        // 将速度转换为每毫秒的距离（米）
        val speedMeterPerMs = (speedKmh * 1000) / (60 * 60 * 1000)
        val distanceM = speedMeterPerMs * deltaTimeMs
        
        // 计算移动距离
        val moveDistance = distanceM * sqrt(deltaX * deltaX + deltaY * deltaY)
        
        // 地球半径（米）
        val earthRadius = 6371000.0
        
        // 计算新的经纬度
        val deltaLat = (moveDistance * deltaY) / earthRadius * (180 / PI)
        val deltaLng = (moveDistance * deltaX) / 
            (earthRadius * cos(currentLat * PI / 180)) * (180 / PI)
        
        return Pair(
            currentLat + deltaLat,
            currentLng + deltaLng
        )
    }
}
```

## 2. 路线模拟技术实现

### 2.1 路线规划与存储

#### 路线数据模型
```kotlin
@Entity(tableName = "routes")
data class Route(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val name: String,
    val waypoints: List<LatLng>,
    val totalDistance: Double,
    val estimatedDuration: Long,
    val createdAt: Long = System.currentTimeMillis(),
    val simulationMode: SimulationMode = SimulationMode.ONCE
)

enum class SimulationMode {
    ONCE,       // 单次执行
    LOOP,       // 循环执行
    PING_PONG   // 往返执行
}

@Dao
interface RouteDao {
    @Query("SELECT * FROM routes ORDER BY createdAt DESC")
    fun getAllRoutes(): Flow<List<Route>>
    
    @Insert
    suspend fun insertRoute(route: Route)
    
    @Delete
    suspend fun deleteRoute(route: Route)
}
```

### 2.2 路线模拟核心算法

#### 路径插值算法
```kotlin
class RouteSimulator {
    private var currentRouteIndex = 0
    private var currentProgress = 0.0
    private var isSimulating = false
    
    fun startRouteSimulation(
        route: Route,
        speedKmh: Double,
        onLocationUpdate: (LatLng) -> Unit
    ) {
        isSimulating = true
        val waypoints = route.waypoints
        
        GlobalScope.launch {
            while (isSimulating && currentRouteIndex < waypoints.size - 1) {
                val startPoint = waypoints[currentRouteIndex]
                val endPoint = waypoints[currentRouteIndex + 1]
                
                // 计算两点间距离
                val distance = calculateDistance(startPoint, endPoint)
                val duration = (distance / speedKmh) * 3600 * 1000 // 毫秒
                
                // 线性插值移动
                val steps = (duration / 100).toInt() // 每100ms更新一次
                for (step in 0..steps) {
                    if (!isSimulating) break
                    
                    val progress = step.toDouble() / steps
                    val currentLat = startPoint.latitude + 
                        (endPoint.latitude - startPoint.latitude) * progress
                    val currentLng = startPoint.longitude + 
                        (endPoint.longitude - startPoint.longitude) * progress
                    
                    withContext(Dispatchers.Main) {
                        onLocationUpdate(LatLng(currentLat, currentLng))
                    }
                    
                    delay(100)
                }
                
                currentRouteIndex++
            }
            
            // 处理循环模式
            when (route.simulationMode) {
                SimulationMode.LOOP -> {
                    currentRouteIndex = 0
                    if (isSimulating) {
                        startRouteSimulation(route, speedKmh, onLocationUpdate)
                    }
                }
                SimulationMode.PING_PONG -> {
                    val reversedRoute = route.copy(
                        waypoints = route.waypoints.reversed()
                    )
                    currentRouteIndex = 0
                    if (isSimulating) {
                        startRouteSimulation(reversedRoute, speedKmh, onLocationUpdate)
                    }
                }
                else -> {
                    isSimulating = false
                }
            }
        }
    }
    
    private fun calculateDistance(point1: LatLng, point2: LatLng): Double {
        // 使用Haversine公式计算两点间距离
        val earthRadius = 6371000.0 // 地球半径（米）
        
        val lat1Rad = Math.toRadians(point1.latitude)
        val lat2Rad = Math.toRadians(point2.latitude)
        val deltaLat = Math.toRadians(point2.latitude - point1.latitude)
        val deltaLng = Math.toRadians(point2.longitude - point1.longitude)
        
        val a = sin(deltaLat / 2) * sin(deltaLat / 2) +
                cos(lat1Rad) * cos(lat2Rad) *
                sin(deltaLng / 2) * sin(deltaLng / 2)
        val c = 2 * atan2(sqrt(a), sqrt(1 - a))
        
        return earthRadius * c
    }
}
```

### 2.3 GPX文件支持

#### GPX解析器实现
```kotlin
class GPXParser {
    fun parseGPXFile(inputStream: InputStream): Route? {
        return try {
            val factory = XmlPullParserFactory.newInstance()
            val parser = factory.newPullParser()
            parser.setInput(inputStream, null)
            
            val waypoints = mutableListOf<LatLng>()
            var routeName = "导入的路线"
            
            var eventType = parser.eventType
            while (eventType != XmlPullParser.END_DOCUMENT) {
                when (eventType) {
                    XmlPullParser.START_TAG -> {
                        when (parser.name) {
                            "trkpt" -> {
                                val lat = parser.getAttributeValue(null, "lat").toDouble()
                                val lon = parser.getAttributeValue(null, "lon").toDouble()
                                waypoints.add(LatLng(lat, lon))
                            }
                            "name" -> {
                                parser.next()
                                if (parser.eventType == XmlPullParser.TEXT) {
                                    routeName = parser.text
                                }
                            }
                        }
                    }
                }
                eventType = parser.next()
            }
            
            if (waypoints.isNotEmpty()) {
                Route(
                    name = routeName,
                    waypoints = waypoints,
                    totalDistance = calculateTotalDistance(waypoints),
                    estimatedDuration = 0L
                )
            } else null
            
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }
    
    fun exportToGPX(route: Route): String {
        return buildString {
            appendLine("""<?xml version="1.0" encoding="UTF-8"?>""")
            appendLine("""<gpx version="1.1" creator="虚拟定位大师">""")
            appendLine("""  <trk>""")
            appendLine("""    <name>${route.name}</name>""")
            appendLine("""    <trkseg>""")
            
            route.waypoints.forEach { point ->
                appendLine("""      <trkpt lat="${point.latitude}" lon="${point.longitude}"/>""")
            }
            
            appendLine("""    </trkseg>""")
            appendLine("""  </trk>""")
            appendLine("""</gpx>""")
        }
    }
}
