# UI/UX设计草图与用户体验

## 1. 应用主界面设计 (MainActivity)

### 1.1 主界面布局结构
```
┌─────────────────────────────────────────────────────────┐
│  🌍 虚拟定位大师                    ⚙️ 📍 ❓          │ <- 顶部工具栏
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │                                                 │   │
│  │              地图显示区域                        │   │
│  │                                                 │   │ <- 地图容器
│  │         📍 当前模拟位置标记                      │   │    (占屏幕70%)
│  │                                                 │   │
│  │                                                 │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │ 🔍 搜索位置或输入经纬度...                       │   │ <- 搜索框
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │  📍 当前位置: 北京市朝阳区...                    │   │ <- 位置信息卡片
│  │  📊 经度: 116.4074  纬度: 39.9042               │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │  🎮 摇杆模式  │ │  🛣️ 路线模拟  │ │   ⭐ 收藏位置    │   │ <- 功能按钮组
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
│                                                         │
│           ┌─────────────────────────────┐               │
│           │     🚀 开始模拟位置          │               │ <- 主要操作按钮
│           │     (大号圆形按钮)           │               │    (绿色/红色切换)
│           └─────────────────────────────┘               │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

### 1.2 状态指示器设计
- **未激活状态**: 灰色圆形按钮，显示"点击开始模拟"
- **激活状态**: 绿色圆形按钮，显示"模拟中..." + 脉冲动画
- **错误状态**: 红色圆形按钮，显示"权限不足" + 设置引导

## 2. 地图选择界面设计

### 2.1 地图交互功能
```
┌─────────────────────────────────────────────────────────┐
│  ← 返回                    地图选点              ✓ 确认  │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │                                                 │   │
│  │                地图区域                         │   │
│  │                                                 │   │
│  │              📍 选中位置                        │   │ <- 可拖拽标记
│  │                                                 │   │
│  │                                                 │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │ 🔍 搜索: 天安门广场                              │   │ <- 搜索框 + 历史记录
│  │ 📋 最近搜索: 故宫, 鸟巢, 水立方...              │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │ 📍 选中位置: 北京市东城区天安门广场               │   │ <- 位置详情卡片
│  │ 📊 经纬度: 116.3974, 39.9063                   │   │
│  │ 🏷️ 标签: [旅游景点] [历史建筑]                  │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │  💾 保存收藏  │ │  📤 分享位置  │ │   🎯 精确定位    │   │ <- 操作按钮
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 3. 摇杆模式界面设计

### 3.1 摇杆控制器UI
```
系统浮动摇杆设计:

┌─────────────────┐
│                 │
│   ┌─────────┐   │  <- 外圈 (半透明灰色)
│   │    ●    │   │  <- 内圈控制点 (蓝色)
│   └─────────┘   │
│                 │
│  ⚙️ 设置  ❌ 关闭  │  <- 控制按钮
└─────────────────┘

摇杆设置面板:
┌─────────────────────────────────────┐
│  🎮 摇杆设置                         │
├─────────────────────────────────────┤
│  📏 摇杆大小: ●────●────○ (中等)      │
│  🎨 透明度:   ●────●────○ (70%)      │
│  📍 位置锁定: ☑️ 启用                 │
│  🚀 移动速度:                        │
│     ○ 步行 (5km/h)                  │
│     ● 跑步 (12km/h)                 │
│     ○ 骑行 (20km/h)                 │
│     ○ 驾车 (60km/h)                 │
│     ○ 自定义: [___] km/h            │
│                                     │
│  ┌─────────┐  ┌─────────────────┐   │
│  │   取消   │  │      保存       │   │
│  └─────────┘  └─────────────────┘   │
└─────────────────────────────────────┘
```

### 3.2 移动轨迹显示
- 在地图上实时显示移动轨迹（虚线）
- 显示当前移动方向箭头
- 速度和距离统计面板

## 4. 路线模拟界面设计

### 4.1 路线规划界面
```
┌─────────────────────────────────────────────────────────┐
│  ← 返回              路线规划              💾 保存路线   │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │                                                 │   │
│  │              地图 + 路线显示                     │   │
│  │                                                 │   │
│  │  A ●─────●─────●─────● B                       │   │ <- 途经点连线
│  │    1     2     3     4                         │   │
│  │                                                 │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │ 🛣️ 路线信息                                      │   │
│  │ 📏 总距离: 15.6 km                              │   │ <- 路线统计
│  │ ⏱️ 预计时间: 18分钟 (驾车)                       │   │
│  │ 📍 途经点: 4个                                   │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────────────────────────────────────────┐   │
│  │ ⚙️ 模拟设置                                       │   │
│  │ 🚀 移动速度: [驾车 ▼]                            │   │ <- 设置面板
│  │ 🔄 执行模式: [单次 ▼] [循环] [往返]               │   │
│  │ ⏰ 开始时间: [立即 ▼] [定时]                      │   │
│  └─────────────────────────────────────────────────┘   │
│                                                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────┐   │
│  │  📤 导出GPX  │ │  📥 导入GPX  │ │   🚀 开始模拟    │   │ <- 操作按钮
│  └─────────────┘ └─────────────┘ └─────────────────┘   │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 5. 设置界面设计

### 5.1 设置页面布局
```
┌─────────────────────────────────────────────────────────┐
│  ← 返回                    设置                          │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  🔧 基础设置                                             │
│  ├─ 🗺️ 默认地图源: [高德地图 ▼]                          │
│  ├─ 🌍 语言设置: [简体中文 ▼]                            │
│  ├─ 🎨 主题模式: [跟随系统 ▼]                            │
│  └─ 🔔 通知设置: [☑️ 启用] [☑️ 声音] [☐ 震动]            │
│                                                         │
│  🎮 摇杆设置                                             │
│  ├─ 📏 默认大小: [中等 ▼]                               │
│  ├─ 🎨 默认透明度: [70% ▼]                              │
│  ├─ 🚀 默认速度: [跑步 ▼]                               │
│  └─ 📍 记住位置: [☑️ 启用]                               │
│                                                         │
│  🛣️ 路线设置                                             │
│  ├─ 💾 自动保存: [☑️ 启用]                               │
│  ├─ 📁 导出格式: [GPX ▼]                                │
│  └─ 🔄 默认模式: [单次 ▼]                               │
│                                                         │
│  🔒 隐私与安全                                           │
│  ├─ 📊 使用统计: [☐ 允许匿名统计]                        │
│  ├─ 🛡️ 反检测: [☑️ 启用GPS抖动]                         │
│  └─ 🗑️ 清除数据: [清除收藏] [清除历史]                   │
│                                                         │
│  ℹ️ 关于应用                                             │
│  ├─ 📱 版本信息: v1.0.0 (Build 1)                      │
│  ├─ 📄 用户协议                                         │
│  ├─ 🔒 隐私政策                                         │
│  ├─ 💬 意见反馈                                         │
│  └─ ⭐ 应用评分                                         │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 6. 权限引导界面设计

### 6.1 新手引导流程
```
第一步: 欢迎页面
┌─────────────────────────────────────────────────────────┐
│                                                         │
│                    🌍                                   │
│              虚拟定位大师                                │
│                                                         │
│         专业的位置模拟工具                               │
│       为开发者和隐私保护而生                             │
│                                                         │
│  ✅ 稳定可靠的位置模拟                                   │
│  ✅ 直观易用的摇杆控制                                   │
│  ✅ 强大的路线规划功能                                   │
│  ✅ 完全本地化，保护隐私                                 │
│                                                         │
│              ┌─────────────────┐                        │
│              │    开始使用     │                        │
│              └─────────────────┘                        │
│                                                         │
└─────────────────────────────────────────────────────────┘

第二步: 权限说明
┌─────────────────────────────────────────────────────────┐
│  📱 权限设置指南                              (1/3)      │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  为了正常使用位置模拟功能，需要进行以下设置：             │
│                                                         │
│  1️⃣ 启用开发者选项                                      │
│     设置 → 关于手机 → 连续点击"版本号"7次                │
│                                                         │
│  2️⃣ 设置模拟位置应用                                    │
│     设置 → 开发者选项 → 选择模拟位置信息应用              │
│     → 选择"虚拟定位大师"                                │
│                                                         │
│  3️⃣ 授予必要权限                                        │
│     位置权限、悬浮窗权限                                 │
│                                                         │
│              ┌─────────────────┐                        │
│              │    下一步       │                        │
│              └─────────────────┘                        │
│                                                         │
└─────────────────────────────────────────────────────────┘
```

## 7. 用户体验优化要点

### 7.1 交互设计原则
- **一键操作**: 主要功能都能在3次点击内完成
- **视觉反馈**: 所有操作都有明确的视觉和触觉反馈
- **错误处理**: 友好的错误提示和解决方案引导
- **无障碍**: 支持TalkBack和大字体显示

### 7.2 性能优化
- **启动速度**: 冷启动时间 < 2秒
- **内存占用**: 运行时内存 < 50MB
- **电池优化**: 后台运行时耗电量最小化
- **网络优化**: 地图数据缓存，减少流量消耗

### 7.3 适配策略
- **屏幕适配**: 支持4.7"-7"屏幕，横竖屏自适应
- **系统适配**: Android 8.0 - Android 14
- **厂商适配**: 针对小米、华为、OPPO等厂商的权限管理优化
