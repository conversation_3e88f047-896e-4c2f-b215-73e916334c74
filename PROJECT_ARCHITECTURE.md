# 虚拟定位大师 - 应用架构设计

## 1. 整体架构概览

### 1.1 架构模式
采用 **MVVM (Model-View-ViewModel)** 架构模式，结合 **Clean Architecture** 原则：

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   MainActivity   │  │  MapFragment   │  │  JoystickOverlay   │  │
│  │   SettingsActivity│  │  RouteFragment │  │  FloatingService   │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    ViewModel Layer                          │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │ MainViewModel │  │ MapViewModel │  │ LocationViewModel   │  │
│  │ RouteViewModel│  │ SettingsVM  │  │ JoystickViewModel   │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Domain Layer                             │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │  Use Cases  │  │ Repositories│  │     Entities        │  │
│  │ - MockLocation│  │ - Location  │  │ - Location         │  │
│  │ - RouteSimul. │  │ - Route     │  │ - Route            │  │
│  │ - Joystick   │  │ - Settings  │  │ - JoystickConfig   │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Data Layer                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐  │
│  │   Database  │  │  SharedPrefs│  │   External APIs     │  │
│  │ - Room DB   │  │ - Settings  │  │ - Map APIs          │  │
│  │ - Favorites │  │ - User Prefs│  │ - Geocoding         │  │
│  │ - Routes    │  │             │  │                     │  │
│  └─────────────┘  └─────────────┘  └─────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 核心组件详解

#### 1.2.1 地图模块 (MapModule)
- **MapFragment**: 主地图界面，支持多种地图源
- **LocationPicker**: 地图点击选择位置功能
- **SearchManager**: 地址搜索和自动补全
- **MapRenderer**: 地图渲染和标记管理

#### 1.2.2 定位模拟服务模块 (LocationMockService)
- **MockLocationService**: 核心前台服务，负责持续提供虚拟位置
- **LocationProvider**: 封装 LocationManager 和 FusedLocationProvider
- **MockLocationManager**: 管理模拟位置的启动、停止和更新
- **PermissionChecker**: 检查和引导用户设置必要权限

#### 1.2.3 摇杆控制模块 (JoystickModule)
- **JoystickOverlayService**: 系统级浮动摇杆服务
- **JoystickView**: 自定义摇杆UI组件
- **MovementCalculator**: 根据摇杆输入计算位置变化
- **SpeedController**: 管理不同移动模式的速度

#### 1.2.4 路线模拟模块 (RouteModule)
- **RouteManager**: 路线规划和管理
- **RouteSimulator**: 沿路线自动移动的核心逻辑
- **GPXParser**: GPX文件导入导出功能
- **WaypointManager**: 途经点管理

#### 1.2.5 数据存储模块 (DataModule)
- **LocationDatabase**: Room数据库，存储收藏位置和路线
- **PreferencesManager**: 用户设置和配置管理
- **BackupManager**: 数据备份和恢复

## 2. 技术栈选择

### 2.1 核心技术
- **开发语言**: Kotlin (100%)
- **UI框架**: Jetpack Compose + 部分传统View
- **架构组件**: 
  - ViewModel, LiveData/StateFlow
  - Room Database
  - Navigation Component
  - WorkManager (后台任务)

### 2.2 地图集成
- **主要地图**: 高德地图 SDK (国内用户友好)
- **备选方案**: OpenStreetMap (开源，无API限制)
- **国际版本**: Google Maps SDK

### 2.3 依赖注入
- **Hilt**: Google推荐的依赖注入框架

### 2.4 网络请求
- **Retrofit2 + OkHttp**: RESTful API调用
- **Gson**: JSON序列化

## 3. 关键技术实现策略

### 3.1 模拟位置核心实现
```kotlin
class MockLocationService : Service() {
    private val locationManager by lazy { 
        getSystemService(Context.LOCATION_SERVICE) as LocationManager 
    }
    
    fun startMockLocation(latitude: Double, longitude: Double) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            locationManager.addTestProvider(
                LocationManager.GPS_PROVIDER,
                false, false, false, false, 
                true, true, true, 
                Criteria.POWER_LOW, Criteria.ACCURACY_FINE
            )
        }
        
        val mockLocation = Location(LocationManager.GPS_PROVIDER).apply {
            this.latitude = latitude
            this.longitude = longitude
            this.accuracy = 1.0f
            this.time = System.currentTimeMillis()
            this.elapsedRealtimeNanos = SystemClock.elapsedRealtimeNanos()
        }
        
        locationManager.setTestProviderLocation(
            LocationManager.GPS_PROVIDER, 
            mockLocation
        )
    }
}
```

### 3.2 前台服务保活策略
- 使用 `startForeground()` 创建持久通知
- 实现 `onStartCommand()` 返回 `START_STICKY`
- 监听系统广播，在服务被杀死后自动重启

### 3.3 权限管理流程
1. 检查是否启用开发者选项
2. 检查是否设置了模拟位置应用
3. 引导用户完成设置（图文教程）
4. 运行时权限检查和申请

## 4. 安全与隐私设计

### 4.1 数据安全
- 所有敏感数据本地加密存储
- 不收集用户真实位置信息
- 不上传用户使用数据

### 4.2 反检测机制
- 模拟GPS信号微小抖动
- 添加海拔高度变化
- 模拟移动轨迹的自然性

### 4.3 合规性
- 内置用户协议和隐私政策
- 明确标注测试用途
- 提供使用限制说明
