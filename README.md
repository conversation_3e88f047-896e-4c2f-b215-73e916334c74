# 虚拟定位大师 (Virtual Location Master)

一款专为Android平台设计的顶级虚拟定位应用，为开发者、测试工程师和注重隐私的用户提供稳定、易用、功能丰富的位置模拟解决方案。

## 🚀 核心功能

### 基础功能
- ✅ 地图选点定位
- ✅ 一键开关控制
- ✅ 后台稳定运行
- ✅ 权限引导系统

### 高级功能
- 🎮 **摇杆模式**: 系统级浮动摇杆，支持实时位置控制
- 🛣️ **路线模拟**: 自动路径执行，支持循环和往返模式
- ⭐ **位置收藏**: 快速位置切换和管理
- 📁 **GPX支持**: 与专业地图工具联动

## 🏗️ 技术架构

### 架构模式
- **MVVM + Clean Architecture**: 清晰的分层架构
- **Jetpack Compose**: 现代化UI框架
- **Hilt依赖注入**: 解耦组件设计
- **Room数据库**: 高效本地存储

### 核心技术栈
- **开发语言**: Kotlin 100%
- **UI框架**: Jetpack Compose + Material Design 3
- **地图SDK**: 高德地图 (国内) / Google Maps (国际)
- **数据库**: Room + SQLite
- **网络**: Retrofit2 + OkHttp
- **异步处理**: Kotlin Coroutines + Flow

## 📁 项目结构

```
app/src/main/java/com/virtuallocation/master/
├── data/                           # 数据层
│   ├── database/                   # 数据库相关
│   │   ├── dao/                   # 数据访问对象
│   │   ├── VirtualLocationDatabase.kt
│   │   └── DatabaseConverters.kt
│   ├── service/                   # 系统服务
│   │   ├── MockLocationService.kt # 模拟位置服务
│   │   └── JoystickOverlayService.kt # 摇杆浮动服务
│   └── receiver/                  # 广播接收器
├── domain/                        # 领域层
│   ├── model/                     # 数据模型
│   │   ├── Location.kt
│   │   ├── Route.kt
│   │   └── JoystickConfig.kt
│   ├── manager/                   # 业务管理器
│   │   └── PermissionManager.kt
│   └── usecase/                   # 用例
├── presentation/                  # 表现层
│   ├── ui/                        # UI组件
│   │   ├── MainActivity.kt
│   │   └── theme/                 # 主题配置
│   └── viewmodel/                 # ViewModel
│       └── MainViewModel.kt
├── di/                           # 依赖注入
│   ├── DatabaseModule.kt
│   └── SystemModule.kt
└── VirtualLocationApplication.kt  # 应用程序类
```

## 🔧 开发环境要求

- **Android Studio**: Arctic Fox 或更高版本
- **Kotlin**: 1.9.10+
- **Gradle**: 8.1.4+
- **Android SDK**: API 26 (Android 8.0) 及以上
- **JDK**: 8 或更高版本

## 🚀 快速开始

### 1. 克隆项目
```bash
git clone https://github.com/your-repo/virtual-location-master.git
cd virtual-location-master
```

### 2. 配置API密钥
在 `app/src/main/AndroidManifest.xml` 中替换高德地图API密钥：
```xml
<meta-data
    android:name="com.amap.api.v2.apikey"
    android:value="YOUR_AMAP_API_KEY" />
```

### 3. 构建项目
```bash
./gradlew assembleDebug
```

### 4. 安装到设备
```bash
./gradlew installDebug
```

## 📱 使用说明

### 权限设置
1. **启用开发者选项**: 设置 → 关于手机 → 连续点击版本号7次
2. **选择模拟位置应用**: 设置 → 开发者选项 → 选择模拟位置信息应用
3. **授予必要权限**: 位置权限、悬浮窗权限等

### 基本使用
1. 打开应用，完成权限设置
2. 在地图上选择目标位置
3. 点击"开始模拟位置"按钮
4. 使用摇杆控制实时移动（可选）

## 🔒 隐私与安全

- **完全本地化**: 所有数据存储在本地，不上传到服务器
- **权限最小化**: 只申请必要的权限
- **开源透明**: 代码完全开源，接受社区审查
- **合规使用**: 内置使用声明和免责条款

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源许可证。

## 🤝 贡献指南

欢迎提交 Issue 和 Pull Request！

1. Fork 本项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📞 联系我们

- **项目主页**: https://github.com/your-repo/virtual-location-master
- **问题反馈**: https://github.com/your-repo/virtual-location-master/issues
- **邮箱**: <EMAIL>

## ⚠️ 免责声明

本应用主要用于软件开发测试和个人隐私保护。请遵守相关应用的用户协议，禁止将本应用用于任何非法或欺诈活动。因滥用本工具产生的一切后果由用户自行承担。

---

**虚拟定位大师** - 让位置测试更简单，让隐私保护更安全！
